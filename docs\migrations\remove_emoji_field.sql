-- Migration: Remove emoji field from homepage_sections table
-- Date: 2025-01-20
-- Description: Remove redundant emoji field since emojis can be included directly in the title

-- Step 1: Update existing records to include emoji in title (if any)
UPDATE homepage_sections 
SET title = CASE 
  WHEN emoji IS NOT NULL AND emoji != '' THEN CONCAT(emoji, ' ', title)
  ELSE title
END
WHERE emoji IS NOT NULL AND emoji != '';

-- Step 2: Remove the emoji column
ALTER TABLE homepage_sections DROP COLUMN IF EXISTS emoji;

-- Step 3: Add comment to document the change
COMMENT ON COLUMN homepage_sections.title IS 'Section title - can include emojis directly (e.g., "🔥 Popular Games")';

-- Verification query to check the migration
-- SELECT id, title, product_ids, order_index, active FROM homepage_sections ORDER BY order_index;
