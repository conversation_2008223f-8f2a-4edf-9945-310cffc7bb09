import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Get user's tenant info
    const { data: { user } } = await supabase.auth.getUser()
    
    let tenantId: string | null = null
    
    if (user) {
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('tenant_id')
        .eq('id', user.id)
        .single()
      
      tenantId = profile?.tenant_id
    }
    
    // If no user or tenant, get main tenant
    if (!tenantId) {
      const { data: mainTenant } = await supabase
        .from('tenants')
        .select('id')
        .eq('slug', 'main')
        .single()
      
      tenantId = mainTenant?.id
    }
    
    if (!tenantId) {
      return NextResponse.json({ error: 'No tenant found' }, { status: 404 })
    }

    // Get active currencies for this tenant
    const { data: currencies, error } = await supabase
      .from('currencies')
      .select('*')
      .eq('tenant_id', tenantId)
      .eq('is_active', true)
      .order('code')

    if (error) throw error

    return NextResponse.json({ currencies: currencies || [] })
  } catch (error) {
    console.error('Error fetching currencies:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
