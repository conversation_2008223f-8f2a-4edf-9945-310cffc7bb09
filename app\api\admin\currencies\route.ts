import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { z } from 'zod'

// Rate limiting map
const rateLimitMap = new Map()

function rateLimit(identifier: string, limit: number = 5, windowMs: number = 60000): boolean {
  const now = Date.now()
  const windowStart = now - windowMs
  
  if (!rateLimitMap.has(identifier)) {
    rateLimitMap.set(identifier, [])
  }
  
  const requests = rateLimitMap.get(identifier)
  const validRequests = requests.filter((time: number) => time > windowStart)
  
  if (validRequests.length >= limit) {
    return false
  }
  
  validRequests.push(now)
  rateLimitMap.set(identifier, validRequests)
  
  return true
}

// Validation schemas
const currencySchema = z.object({
  code: z.string().regex(/^[A-Z]{3}$/, "Currency code must be 3 uppercase letters"),
  name: z.string().min(1).max(50),
  symbol: z.string().min(1).max(10),
  exchange_rate: z.number().positive().max(999999),
  is_active: z.boolean().optional()
})

const updateRateSchema = z.object({
  code: z.string().regex(/^[A-Z]{3}$/),
  exchange_rate: z.number().positive().max(999999)
})

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get currencies for this tenant
    const { data: currencies, error } = await supabase
      .from('currencies')
      .select('*')
      .eq('tenant_id', profile.tenant_id)
      .order('code')

    if (error) throw error

    return NextResponse.json({ currencies })
  } catch (error) {
    console.error('Error fetching currencies:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = currencySchema.parse(body)

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Prevent creating USD (it's hardcoded)
    if (validatedData.code === 'USD') {
      return NextResponse.json({ error: 'USD is the default currency and cannot be modified' }, { status: 400 })
    }

    // Insert new currency
    const { data: currency, error } = await supabase
      .from('currencies')
      .insert({
        ...validatedData,
        tenant_id: profile.tenant_id
      })
      .select()
      .single()

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        return NextResponse.json({ error: 'Currency already exists' }, { status: 409 })
      }
      throw error
    }

    return NextResponse.json({ currency }, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 })
    }
    console.error('Error creating currency:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Rate limiting
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { code, exchange_rate } = updateRateSchema.parse(body)

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Prevent modifying USD rate (it's always 1.0)
    if (code === 'USD') {
      return NextResponse.json({ error: 'USD exchange rate cannot be modified' }, { status: 400 })
    }

    // Update exchange rate
    const { data: currency, error } = await supabase
      .from('currencies')
      .update({ 
        exchange_rate,
        updated_at: new Date().toISOString()
      })
      .eq('code', code)
      .eq('tenant_id', profile.tenant_id)
      .select()
      .single()

    if (error) throw error

    if (!currency) {
      return NextResponse.json({ error: 'Currency not found' }, { status: 404 })
    }

    return NextResponse.json({ currency })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 })
    }
    console.error('Error updating currency:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Rate limiting
    const identifier = request.ip || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { code, is_active } = body

    if (!code || typeof is_active !== 'boolean') {
      return NextResponse.json({ error: 'Invalid request body' }, { status: 400 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Prevent disabling USD
    if (code === 'USD' && !is_active) {
      return NextResponse.json({ error: 'USD cannot be disabled' }, { status: 400 })
    }

    // Update currency status
    const { data: currency, error } = await supabase
      .from('currencies')
      .update({ 
        is_active,
        updated_at: new Date().toISOString()
      })
      .eq('code', code)
      .eq('tenant_id', profile.tenant_id)
      .select()
      .single()

    if (error) throw error

    if (!currency) {
      return NextResponse.json({ error: 'Currency not found' }, { status: 404 })
    }

    return NextResponse.json({ currency })
  } catch (error) {
    console.error('Error updating currency status:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
