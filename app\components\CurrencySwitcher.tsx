'use client'

import { useData } from '../contexts/DataContext'
import { ChevronDown } from 'lucide-react'

export function CurrencySwitcher() {
  const { currencies, selectedCurrency, setSelectedCurrency } = useData()

  if (currencies.length <= 1) {
    return null
  }

  return (
    <div className="relative">
      <select
        value={selectedCurrency}
        onChange={(e) => setSelectedCurrency(e.target.value)}
        className="appearance-none bg-gray-800 text-white px-3 py-2 pr-8 rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none text-sm"
      >
        {currencies.map((currency) => (
          <option key={currency.code} value={currency.code}>
            {currency.symbol} {currency.code}
          </option>
        ))}
      </select>
      <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
    </div>
  )
}
