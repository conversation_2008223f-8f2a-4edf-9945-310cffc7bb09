"use client"

import React, { create<PERSON>ontext, useContext, useState, useEffect, ReactNode } from "react"
import type { Product, User, Order, BannerSlide, HomepageSection } from "../types"
import { validateData, sanitizeObject } from "../lib/validations"
import { productSchema, userSchema, orderSchema, bannerSlideSchema, homepageSectionSchema } from "../lib/validations"
import { useTenant } from "./TenantContext"
import { supabase } from "../lib/supabase"

interface DataContextType {
  // Products
  products: Product[]
  setProducts: React.Dispatch<React.SetStateAction<Product[]>>
  updateProduct: (product: Product) => Promise<{ success: boolean; error?: string }>
  deleteProduct: (productId: string) => Promise<{ success: boolean; error?: string }>
  addProduct: (product: Product) => Promise<{ success: boolean; error?: string }>

  // Users
  users: User[]
  setUsers: React.Dispatch<React.SetStateAction<User[]>>
  updateUser: (user: User) => Promise<{ success: boolean; error?: string }>
  deleteUser: (userId: string) => Promise<{ success: boolean; error?: string }>
  addUser: (user: User) => Promise<{ success: boolean; error?: string }>
  currentUser: User | null
  setCurrentUser: React.Dispatch<React.SetStateAction<User | null>>

  // Orders
  orders: Order[]
  setOrders: React.Dispatch<React.SetStateAction<Order[]>>
  updateOrder: (order: Order) => Promise<{ success: boolean; error?: string }>
  deleteOrder: (orderId: string) => Promise<{ success: boolean; error?: string }>
  addOrder: (order: Order) => Promise<{ success: boolean; error?: string }>

  // Homepage Configuration
  banners: BannerSlide[]
  setBanners: React.Dispatch<React.SetStateAction<BannerSlide[]>>
  updateBanner: (banner: BannerSlide) => Promise<{ success: boolean; error?: string }>
  deleteBanner: (bannerId: string) => Promise<{ success: boolean; error?: string }>
  addBanner: (banner: BannerSlide) => Promise<{ success: boolean; error?: string }>

  homepageSections: HomepageSection[]
  setHomepageSections: React.Dispatch<React.SetStateAction<HomepageSection[]>>
  updateHomepageSection: (section: HomepageSection) => Promise<{ success: boolean; error?: string }>
  deleteHomepageSection: (sectionId: string) => Promise<{ success: boolean; error?: string }>
  addHomepageSection: (section: HomepageSection) => Promise<{ success: boolean; error?: string }>

  // Utility functions
  refreshData: () => Promise<void>
  isLoading: boolean
  error: string | null
  clearError: () => void
}

const DataContext = createContext<DataContextType | undefined>(undefined)

export function useData() {
  const context = useContext(DataContext)
  if (context === undefined) {
    throw new Error("useData must be used within a DataProvider")
  }
  return context
}

interface DataProviderProps {
  children: ReactNode
}

export function DataProvider({ children }: DataProviderProps) {
  const { tenant, getTenantFilter, isMultiTenantMode } = useTenant()

  // State management for all data
  const [products, setProducts] = useState<Product[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [orders, setOrders] = useState<Order[]>([])
  const [banners, setBanners] = useState<BannerSlide[]>([])
  const [homepageSections, setHomepageSections] = useState<HomepageSection[]>([])
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Initialize data when tenant is available
  useEffect(() => {
    if (tenant) {
      initializeData()
    }
  }, [tenant])

  // Initialize current user from Supabase auth
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (user) {
          // Get user profile with tenant information
          const { data: profile } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('id', user.id)
            .single()

          if (profile) {
            setCurrentUser({
              id: profile.id,
              email: user.email || '',
              name: profile.name,
              role: profile.role,
              walletBalance: profile.wallet_balance || 0,
              avatar: profile.avatar,
              phone: profile.phone,
              createdAt: profile.created_at
            })
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
      }
    }

    initializeAuth()
  }, [])

  // Initialize data based on tenant
  const initializeData = async () => {
    if (!tenant) return

    try {
      setIsLoading(true)
      clearError()

      // Always fetch real data from Supabase with tenant filtering
      await fetchTenantData()
    } catch (error) {
      handleError(error, 'تحميل البيانات')
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch tenant-specific data from Supabase
  const fetchTenantData = async () => {
    if (!tenant) return

    const tenantFilter = getTenantFilter()

    // Fetch products with category data
    const { data: productsData } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          slug,
          description,
          image,
          created_at,
          updated_at,
          tenant_id
        ),
        packages (
          *,
          digital_codes (*)
        ),
        custom_fields (*),
        dropdowns (
          *,
          dropdown_options (*)
        )
      `)
      .match(tenantFilter)

    if (productsData) {
      setProducts(productsData.map(mapDatabaseProduct))
    }

    // Fetch orders
    const { data: ordersData } = await supabase
      .from('orders')
      .select('*')
      .match(tenantFilter)

    if (ordersData) {
      setOrders(ordersData.map(mapDatabaseOrder))
    }

    // Fetch banners
    const { data: bannersData } = await supabase
      .from('banner_slides')
      .select('*')
      .match(tenantFilter)
      .order('order_index')

    if (bannersData) {
      setBanners(bannersData.map(mapDatabaseBanner))
    }

    // Fetch homepage sections
    const { data: sectionsData } = await supabase
      .from('homepage_sections')
      .select('*')
      .match(tenantFilter)
      .order('order_index')

    if (sectionsData) {
      setHomepageSections(sectionsData.map(mapDatabaseSection))
    }
  }

  // Database mapping functions
  const mapDatabaseProduct = (data: any): Product => ({
    id: data.id,
    slug: data.slug,
    title: data.title,
    description: data.description,
    coverImage: data.cover_image,
    category: data.category,
    category_id: data.category_id,
    categoryData: data.categories ? {
      id: data.categories.id,
      tenant_id: data.categories.tenant_id,
      name: data.categories.name,
      slug: data.categories.slug,
      description: data.categories.description,
      image: data.categories.image,
      created_at: data.categories.created_at,
      updated_at: data.categories.updated_at
    } : undefined,
    tags: data.tags || [],
    rating: data.rating || 0,
    commentCount: data.comment_count || 0,
    packages: data.packages?.map((pkg: any) => ({
      id: pkg.id,
      name: pkg.name,
      price: pkg.price,
      originalPrice: pkg.original_price,
      discount: pkg.discount,
      image: pkg.image,
      use_product_image: pkg.use_product_image,
      image_reference_type: pkg.image_reference_type,
      description: pkg.description,
      hasDigitalCodes: pkg.has_digital_codes,
      digitalCodes: pkg.digital_codes?.map((code: any) => ({
        id: code.id,
        key: code.key_encrypted,
        used: code.used,
        assignedToOrderId: code.assigned_to_order_id,
        assignedAt: code.assigned_at,
        viewedCount: code.viewed_count,
        lastViewedAt: code.last_viewed_at
      })) || []
    })) || [],
    customFields: data.custom_fields?.map((field: any) => ({
      id: field.id,
      label: field.label,
      type: field.field_type,
      required: field.required,
      placeholder: field.placeholder
    })) || [],
    dropdowns: data.dropdowns?.map((dropdown: any) => ({
      id: dropdown.id,
      label: dropdown.label,
      required: dropdown.required,
      options: dropdown.dropdown_options?.map((option: any) => ({
        value: option.value,
        label: option.label
      })) || []
    })) || [],
    featured: data.featured,
    popular: data.popular
  })

  const mapDatabaseOrder = (data: any): Order => ({
    id: data.id,
    userId: data.user_id,
    productId: data.product_id,
    packageId: data.package_id,
    amount: data.amount,
    status: data.status,
    createdAt: data.created_at,
    customData: data.custom_data || {}
  })

  const mapDatabaseBanner = (data: any): BannerSlide => ({
    id: data.id,
    title: data.title,
    subtitle: data.subtitle,
    image: data.image,
    linkType: data.link_type,
    linkValue: data.link_value,
    active: data.active,
    order: data.order_index
  })

  const mapDatabaseSection = (data: any): HomepageSection => ({
    id: data.id,
    title: data.title,
    productIds: data.product_ids || [],
    order: data.order_index,
    active: data.active
  })

  // Utility functions
  const clearError = () => setError(null)

  const handleError = (error: unknown, operation: string): string => {
    const errorMessage = error instanceof Error ? error.message : `خطأ في ${operation}`
    setError(errorMessage)
    console.error(`Error in ${operation}:`, error)
    return errorMessage
  }

  // Product management functions with validation and tenant awareness
  const updateProduct = async (updatedProduct: Product): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      if (!tenant) {
        const errorMsg = 'لا يمكن تحديث المنتج بدون معرف المستأجر'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // Sanitize input data
      const sanitizedProduct = sanitizeObject(updatedProduct)

      // Validate product data
      const validation = validateData(productSchema, sanitizedProduct)
      if (!validation.success) {
        const errorMsg = validation.errors.join(', ')
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      if (isMultiTenantMode) {
        // Update in Supabase with tenant filtering
        const { error: updateError } = await supabase
          .from('products')
          .update({
            slug: updatedProduct.slug,
            title: updatedProduct.title,
            description: updatedProduct.description,
            cover_image: updatedProduct.coverImage,
            category: updatedProduct.category,
            tags: updatedProduct.tags,
            rating: updatedProduct.rating,
            comment_count: updatedProduct.commentCount,
            featured: updatedProduct.featured,
            popular: updatedProduct.popular,
            updated_at: new Date().toISOString()
          })
          .eq('id', updatedProduct.id)
          .eq('tenant_id', tenant.id)

        if (updateError) {
          throw updateError
        }
      } else {
        // Mock update for development
        await new Promise(resolve => setTimeout(resolve, 500))
      }

      setProducts(prev => prev.map(product =>
        product.id === updatedProduct.id ? validation.data as Product : product
      ))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'تحديث المنتج')
      return { success: false, error: errorMsg }
    }
  }

  const deleteProduct = async (productId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      if (!productId) {
        const errorMsg = 'معرف المنتج مطلوب'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      if (!tenant) {
        const errorMsg = 'لا يمكن حذف المنتج بدون معرف المستأجر'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      if (isMultiTenantMode) {
        // Delete from Supabase with tenant filtering
        const { error: deleteError } = await supabase
          .from('products')
          .delete()
          .eq('id', productId)
          .eq('tenant_id', tenant.id)

        if (deleteError) {
          throw deleteError
        }
      } else {
        // Mock delete for development
        await new Promise(resolve => setTimeout(resolve, 500))
      }

      setProducts(prev => prev.filter(product => product.id !== productId))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'حذف المنتج')
      return { success: false, error: errorMsg }
    }
  }

  const addProduct = async (newProduct: Product): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      if (!tenant) {
        const errorMsg = 'لا يمكن إضافة المنتج بدون معرف المستأجر'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // Sanitize input data
      const sanitizedProduct = sanitizeObject(newProduct)

      // Validate product data
      const validation = validateData(productSchema, sanitizedProduct)
      if (!validation.success) {
        const errorMsg = validation.errors.join(', ')
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // Check for duplicate slug
      const existingProduct = products.find(p => p.slug === validation.data.slug)
      if (existingProduct) {
        const errorMsg = 'الرابط المختصر موجود بالفعل'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      if (isMultiTenantMode) {
        // Add to Supabase with tenant filtering
        const { error: insertError } = await supabase
          .from('products')
          .insert({
            ...validation.data,
            tenant_id: tenant.id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })

        if (insertError) {
          throw insertError
        }
      }

      setProducts(prev => [...prev, validation.data as Product])

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'إضافة المنتج')
      return { success: false, error: errorMsg }
    }
  }

  // User management functions with validation
  const updateUser = async (updatedUser: User): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      // Sanitize input data
      const sanitizedUser = sanitizeObject(updatedUser)

      // Validate user data
      const validation = validateData(userSchema, sanitizedUser)
      if (!validation.success) {
        const errorMsg = validation.errors.join(', ')
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setUsers(prev => prev.map(user =>
        user.id === updatedUser.id ? validation.data as User : user
      ))

      // Update current user if it's the same user
      if (currentUser && currentUser.id === updatedUser.id) {
        setCurrentUser(validation.data as User)
      }

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'تحديث المستخدم')
      return { success: false, error: errorMsg }
    }
  }

  const deleteUser = async (userId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      if (!userId) {
        const errorMsg = 'معرف المستخدم مطلوب'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // Prevent deleting current user
      if (currentUser && currentUser.id === userId) {
        const errorMsg = 'لا يمكن حذف المستخدم الحالي'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setUsers(prev => prev.filter(user => user.id !== userId))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'حذف المستخدم')
      return { success: false, error: errorMsg }
    }
  }

  const addUser = async (newUser: User): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      // Sanitize input data
      const sanitizedUser = sanitizeObject(newUser)

      // Validate user data
      const validation = validateData(userSchema, sanitizedUser)
      if (!validation.success) {
        const errorMsg = validation.errors.join(', ')
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // Check for duplicate email
      const existingUser = users.find(u => u.email === validation.data.email)
      if (existingUser) {
        const errorMsg = 'البريد الإلكتروني موجود بالفعل'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setUsers(prev => [...prev, validation.data as User])

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'إضافة المستخدم')
      return { success: false, error: errorMsg }
    }
  }

  // Order management functions
  const updateOrder = async (updatedOrder: Order): Promise<{ success: boolean; error?: string }> => {
    try {
      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setOrders(prev => prev.map(order =>
        order.id === updatedOrder.id ? updatedOrder : order
      ))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'تحديث الطلب')
      return { success: false, error: errorMsg }
    }
  }

  const deleteOrder = async (orderId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setOrders(prev => prev.filter(order => order.id !== orderId))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'حذف الطلب')
      return { success: false, error: errorMsg }
    }
  }

  const addOrder = async (newOrder: Order): Promise<{ success: boolean; error?: string }> => {
    try {
      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setOrders(prev => [...prev, newOrder])

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'إضافة الطلب')
      return { success: false, error: errorMsg }
    }
  }

  // Banner management functions
  const updateBanner = async (updatedBanner: BannerSlide): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      if (!tenant) {
        const errorMsg = 'لا يمكن تحديث البانر بدون معرف المستأجر'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // Validate banner data
      const validation = validateData(bannerSlideSchema, updatedBanner)
      if (!validation.success) {
        return { success: false, error: validation.errors.join(', ') }
      }

      if (isMultiTenantMode) {
        // Update in Supabase with tenant filtering
        const { error: updateError } = await supabase
          .from('banner_slides')
          .update({
            title: updatedBanner.title,
            subtitle: updatedBanner.subtitle,
            image: updatedBanner.image,
            link_type: updatedBanner.linkType,
            link_value: updatedBanner.linkValue,
            active: updatedBanner.active,
            order_index: updatedBanner.order,
            updated_at: new Date().toISOString()
          })
          .eq('id', updatedBanner.id)
          .eq('tenant_id', tenant.id)

        if (updateError) {
          throw updateError
        }
      }

      setBanners(prev => prev.map(banner =>
        banner.id === updatedBanner.id ? updatedBanner : banner
      ))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'تحديث البانر')
      return { success: false, error: errorMsg }
    }
  }

  const deleteBanner = async (bannerId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      if (!bannerId) {
        const errorMsg = 'معرف البانر مطلوب'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      if (!tenant) {
        const errorMsg = 'لا يمكن حذف البانر بدون معرف المستأجر'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      if (isMultiTenantMode) {
        // Delete from Supabase with tenant filtering
        const { error: deleteError } = await supabase
          .from('banner_slides')
          .delete()
          .eq('id', bannerId)
          .eq('tenant_id', tenant.id)

        if (deleteError) {
          throw deleteError
        }
      }

      setBanners(prev => prev.filter(banner => banner.id !== bannerId))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'حذف البانر')
      return { success: false, error: errorMsg }
    }
  }

  const addBanner = async (newBanner: BannerSlide): Promise<{ success: boolean; error?: string }> => {
    try {
      clearError()

      if (!tenant) {
        const errorMsg = 'لا يمكن إضافة البانر بدون معرف المستأجر'
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      // Validate banner data
      const validation = validateData(bannerSlideSchema, newBanner)
      if (!validation.success) {
        const errorMsg = validation.errors.join(', ')
        setError(errorMsg)
        return { success: false, error: errorMsg }
      }

      if (isMultiTenantMode) {
        // Add to Supabase with tenant filtering
        const { error: insertError } = await supabase
          .from('banner_slides')
          .insert({
            ...validation.data,
            tenant_id: tenant.id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })

        if (insertError) {
          throw insertError
        }
      }

      setBanners(prev => [...prev, validation.data as BannerSlide])

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'إضافة البانر')
      return { success: false, error: errorMsg }
    }
  }

  // Homepage section management functions
  const updateHomepageSection = async (updatedSection: HomepageSection): Promise<{ success: boolean; error?: string }> => {
    try {
      if (!tenant) {
        return { success: false, error: 'لا يوجد متجر محدد' }
      }

      // Validate section data
      const validation = validateData(homepageSectionSchema, updatedSection)
      if (!validation.success) {
        return { success: false, error: validation.errors.join(', ') }
      }

      // Update in Supabase
      const { error } = await supabase
        .from('homepage_sections')
        .update({
          title: updatedSection.title,
          product_ids: updatedSection.productIds,
          order_index: updatedSection.order,
          active: updatedSection.active,
          updated_at: new Date().toISOString()
        })
        .eq('id', updatedSection.id)
        .eq('tenant_id', tenant.id)

      if (error) {
        console.error('Error updating homepage section:', error)
        return { success: false, error: 'فشل في تحديث القسم' }
      }

      // Update local state
      setHomepageSections(prev => prev.map(section =>
        section.id === updatedSection.id ? updatedSection : section
      ))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'تحديث القسم')
      return { success: false, error: errorMsg }
    }
  }

  const deleteHomepageSection = async (sectionId: string): Promise<{ success: boolean; error?: string }> => {
    try {
      // TODO: Replace with actual API call to Supabase
      await new Promise(resolve => setTimeout(resolve, 500)) // Simulate API delay

      setHomepageSections(prev => prev.filter(section => section.id !== sectionId))

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'حذف القسم')
      return { success: false, error: errorMsg }
    }
  }

  const addHomepageSection = async (newSection: HomepageSection): Promise<{ success: boolean; error?: string }> => {
    try {
      if (!tenant) {
        return { success: false, error: 'لا يوجد متجر محدد' }
      }

      // Validate section data
      const validation = validateData(homepageSectionSchema, newSection)
      if (!validation.success) {
        return { success: false, error: validation.errors.join(', ') }
      }

      // Insert into Supabase
      const { data, error } = await supabase
        .from('homepage_sections')
        .insert({
          id: newSection.id,
          tenant_id: tenant.id,
          title: newSection.title,
          product_ids: newSection.productIds,
          order_index: newSection.order,
          active: newSection.active
        })
        .select()
        .single()

      if (error) {
        console.error('Error adding homepage section:', error)
        return { success: false, error: 'فشل في إضافة القسم' }
      }

      // Update local state
      setHomepageSections(prev => [...prev, newSection])

      return { success: true }
    } catch (error) {
      const errorMsg = handleError(error, 'إضافة القسم')
      return { success: false, error: errorMsg }
    }
  }

  // Utility function to refresh all data
  const refreshData = async (): Promise<void> => {
    try {
      setIsLoading(true)
      clearError()

      // Fetch real data from Supabase with tenant filtering
      await fetchTenantData()
    } catch (error) {
      handleError(error, 'تحديث البيانات')
    } finally {
      setIsLoading(false)
    }
  }

  const value: DataContextType = {
    // Products
    products,
    setProducts,
    updateProduct,
    deleteProduct,
    addProduct,

    // Users
    users,
    setUsers,
    updateUser,
    deleteUser,
    addUser,
    currentUser,
    setCurrentUser,

    // Orders
    orders,
    setOrders,
    updateOrder,
    deleteOrder,
    addOrder,

    // Homepage Configuration
    banners,
    setBanners,
    updateBanner,
    deleteBanner,
    addBanner,

    homepageSections,
    setHomepageSections,
    updateHomepageSection,
    deleteHomepageSection,
    addHomepageSection,

    // Utility
    refreshData,
    isLoading,
    error,
    clearError,
  }

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  )
}
