-- Migration: Add Categories Table for Product Category Management
-- Date: 2025-01-19
-- Description: Create categories table with tenant isolation and update packages for image optimization

-- 1. Create categories table
CREATE TABLE categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  name TEXT, -- Optional display name
  slug TEXT NOT NULL, -- Required URL-friendly identifier
  description TEXT, -- Optional description
  image TEXT NOT NULL, -- Required image URL
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Ensure unique slug per tenant
  CONSTRAINT unique_category_slug_per_tenant UNIQUE (tenant_id, slug)
);

-- 2. Create indexes for performance
CREATE INDEX idx_categories_tenant_id ON categories(tenant_id);
CREATE INDEX idx_categories_slug ON categories(slug);

-- 3. Enable Row Level Security
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- 4. Create RLS policies for categories
CREATE POLICY "Categories are viewable by everyone in same tenant" ON categories FOR SELECT USING (
  tenant_id = (
    SELECT COALESCE(
      (SELECT tenant_id FROM user_profiles WHERE id = auth.uid()),
      (SELECT id FROM tenants WHERE slug = 'main')
    )
  )
);

CREATE POLICY "Admins can manage categories in their tenant" ON categories FOR ALL USING (
  EXISTS (
    SELECT 1 FROM user_profiles up
    WHERE up.id = auth.uid()
    AND up.role = 'admin'
    AND (up.tenant_id = categories.tenant_id OR (up.settings->>'is_super_admin')::boolean = true)
  )
);

-- 5. Add category_id foreign key to products table
ALTER TABLE products ADD COLUMN category_id UUID REFERENCES categories(id) ON DELETE SET NULL;

-- 6. Create index for products.category_id
CREATE INDEX idx_products_category_id ON products(category_id);

-- 7. Add image optimization fields to packages table
ALTER TABLE packages ADD COLUMN use_product_image BOOLEAN DEFAULT FALSE;
ALTER TABLE packages ADD COLUMN image_reference_type TEXT DEFAULT 'url' CHECK (image_reference_type IN ('url', 'product_image'));

-- 8. Create index for package image optimization
CREATE INDEX idx_packages_use_product_image ON packages(use_product_image);

-- 9. Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 10. Create triggers for updated_at
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 11. Insert default categories for existing tenants
INSERT INTO categories (tenant_id, name, slug, description, icon, color, sort_order)
SELECT 
  t.id as tenant_id,
  'ألعاب' as name,
  'games' as slug,
  'ألعاب الفيديو والترفيه' as description,
  '🎮' as icon,
  '#6366f1' as color,
  1 as sort_order
FROM tenants t
WHERE NOT EXISTS (
  SELECT 1 FROM categories c WHERE c.tenant_id = t.id AND c.slug = 'games'
);

INSERT INTO categories (tenant_id, name, slug, description, icon, color, sort_order)
SELECT 
  t.id as tenant_id,
  'خدمات رقمية' as name,
  'digital-services' as slug,
  'خدمات وتطبيقات رقمية' as description,
  '💻' as icon,
  '#10b981' as color,
  2 as sort_order
FROM tenants t
WHERE NOT EXISTS (
  SELECT 1 FROM categories c WHERE c.tenant_id = t.id AND c.slug = 'digital-services'
);

INSERT INTO categories (tenant_id, name, slug, description, icon, color, sort_order)
SELECT 
  t.id as tenant_id,
  'بطاقات هدايا' as name,
  'gift-cards' as slug,
  'بطاقات الهدايا والشحن' as description,
  '🎁' as icon,
  '#f59e0b' as color,
  3 as sort_order
FROM tenants t
WHERE NOT EXISTS (
  SELECT 1 FROM categories c WHERE c.tenant_id = t.id AND c.slug = 'gift-cards'
);

-- 12. Migrate existing product categories to new system
-- This will create categories for existing category strings and link products
DO $$
DECLARE
    tenant_record RECORD;
    category_record RECORD;
    existing_category RECORD;
BEGIN
    -- For each tenant
    FOR tenant_record IN SELECT id, slug FROM tenants LOOP
        -- Get unique categories from products in this tenant
        FOR category_record IN 
            SELECT DISTINCT category 
            FROM products 
            WHERE tenant_id = tenant_record.id 
            AND category IS NOT NULL 
            AND category != ''
        LOOP
            -- Check if category already exists
            SELECT * INTO existing_category 
            FROM categories 
            WHERE tenant_id = tenant_record.id 
            AND name = category_record.category;
            
            -- If category doesn't exist, create it
            IF NOT FOUND THEN
                INSERT INTO categories (tenant_id, name, slug, description, sort_order)
                VALUES (
                    tenant_record.id,
                    category_record.category,
                    lower(replace(replace(category_record.category, ' ', '-'), 'ا', 'a')),
                    'فئة تم إنشاؤها تلقائياً من البيانات الموجودة',
                    100 -- Put migrated categories at the end
                );
                
                -- Get the newly created category
                SELECT * INTO existing_category 
                FROM categories 
                WHERE tenant_id = tenant_record.id 
                AND name = category_record.category;
            END IF;
            
            -- Update products to reference the category
            UPDATE products 
            SET category_id = existing_category.id 
            WHERE tenant_id = tenant_record.id 
            AND category = category_record.category;
        END LOOP;
    END LOOP;
END $$;

-- 13. Add constraint to ensure products have either category or category_id
-- (We'll keep both for backward compatibility during transition)
-- ALTER TABLE products ADD CONSTRAINT check_category_or_category_id 
-- CHECK (category IS NOT NULL OR category_id IS NOT NULL);

COMMENT ON TABLE categories IS 'Product categories with tenant isolation and management features';
COMMENT ON COLUMN categories.tenant_id IS 'Tenant identifier for multi-tenant isolation';
COMMENT ON COLUMN categories.slug IS 'URL-friendly identifier for category';
COMMENT ON COLUMN categories.icon IS 'Emoji or icon identifier for category display';
COMMENT ON COLUMN categories.color IS 'Hex color code for category theming';
COMMENT ON COLUMN categories.sort_order IS 'Custom ordering for category display';
COMMENT ON COLUMN packages.use_product_image IS 'Whether to use parent product image instead of separate package image';
COMMENT ON COLUMN packages.image_reference_type IS 'Type of image reference: url or product_image';
