"use client"

import { useState } from "react"
import { supabase } from "../lib/supabase"

export default function TenantCreator() {
  const [isCreating, setIsCreating] = useState(false)
  const [message, setMessage] = useState("")

  // Only show this component in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const createTestTenants = async () => {
    setIsCreating(true)
    setMessage("⏳ جاري إنشاء المتاجر التجريبية...")

    try {
      // Create store2 tenant
      const { error: store2Error } = await supabase
        .from('tenants')
        .upsert({
          id: '550e8400-e29b-41d4-a716-446655440999',
          name: 'متجر الإلكترونيات',
          slug: 'store2',
          status: 'active',
          theme_config: {
            primaryColor: '#2563eb',
            secondaryColor: '#1d4ed8',
            accentColor: '#f59e0b',
            backgroundColor: '#111827',
            textColor: '#ffffff'
          },
          settings: {
            features: {
              digitalCodes: true,
              walletSystem: true,
              multiCurrency: false
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (store2Error) {
        console.error('Store2 creation error:', store2Error)
      }

      // Create store3 tenant
      const { error: store3Error } = await supabase
        .from('tenants')
        .upsert({
          id: '550e8400-e29b-41d4-a716-446655440998',
          name: 'متجر الملابس',
          slug: 'store3',
          status: 'active',
          theme_config: {
            primaryColor: '#dc2626',
            secondaryColor: '#b91c1c',
            accentColor: '#f59e0b',
            backgroundColor: '#111827',
            textColor: '#ffffff'
          },
          settings: {
            features: {
              digitalCodes: false,
              walletSystem: true,
              multiCurrency: false
            }
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (store3Error) {
        console.error('Store3 creation error:', store3Error)
      }

      if (!store2Error && !store3Error) {
        setMessage("✅ تم إنشاء المتاجر بنجاح! يمكنك الآن زيارة:")
        setTimeout(() => {
          setMessage(prev => prev + "\n🔗 store2.localhost:3001\n🔗 store3.localhost:3001")
        }, 1000)
      } else {
        setMessage("❌ حدث خطأ في إنشاء بعض المتاجر")
      }
    } catch (error) {
      console.error('Error creating tenants:', error)
      setMessage("❌ حدث خطأ أثناء إنشاء المتاجر")
    } finally {
      setIsCreating(false)
    }
  }

  const addSampleDataToStore = async (tenantId: string, storeName: string) => {
    try {
      // Add sample products for the store
      const sampleProducts = [
        {
          id: `${tenantId}-product-1`,
          tenant_id: tenantId,
          slug: 'sample-product-1',
          title: `منتج تجريبي - ${storeName}`,
          description: `هذا منتج تجريبي خاص بـ ${storeName}`,
          cover_image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=800&h=600&fit=crop',
          category: 'تجريبي',
          tags: ['تجريبي', 'اختبار'],
          rating: 4.5,
          comment_count: 100,
          featured: true,
          popular: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ]

      const { error: productsError } = await supabase
        .from('products')
        .upsert(sampleProducts)

      // Add sample banner
      const sampleBanner = {
        id: `${tenantId}-banner-1`,
        tenant_id: tenantId,
        title: `مرحباً بك في ${storeName}`,
        subtitle: 'متجر مختلف تماماً عن المتجر الرئيسي',
        image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=1200&h=400&fit=crop',
        link_type: 'none',
        active: true,
        order_index: 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      const { error: bannerError } = await supabase
        .from('banner_slides')
        .upsert([sampleBanner])

      if (!productsError && !bannerError) {
        return true
      }
    } catch (error) {
      console.error(`Error adding sample data to ${storeName}:`, error)
    }
    return false
  }

  const createTenantsWithData = async () => {
    setIsCreating(true)
    setMessage("⏳ جاري إنشاء المتاجر مع البيانات التجريبية...")

    try {
      await createTestTenants()
      
      // Add sample data to both stores
      const store2Success = await addSampleDataToStore('550e8400-e29b-41d4-a716-446655440999', 'متجر الإلكترونيات')
      const store3Success = await addSampleDataToStore('550e8400-e29b-41d4-a716-446655440998', 'متجر الملابس')

      if (store2Success && store3Success) {
        setMessage("✅ تم إنشاء المتاجر مع البيانات التجريبية!\n🔗 store2.localhost:3001\n🔗 store3.localhost:3001")
      }
    } catch (error) {
      console.error('Error:', error)
      setMessage("❌ حدث خطأ")
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <div className="fixed top-4 right-4 bg-gray-800 border border-gray-700 rounded-lg p-4 shadow-lg z-50 max-w-sm">
      <h3 className="text-white font-bold mb-2">🏗️ إنشاء متاجر تجريبية</h3>
      
      <div className="space-y-2">
        <p className="text-gray-300 text-sm">
          إنشاء متاجر منفصلة للاختبار
        </p>
        
        <button
          onClick={createTenantsWithData}
          disabled={isCreating}
          className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
        >
          {isCreating ? "⏳ جاري الإنشاء..." : "🏪 إنشاء متاجر تجريبية"}
        </button>
        
        {message && (
          <div className="mt-2 p-2 bg-gray-700 rounded text-xs text-white whitespace-pre-line">
            {message}
          </div>
        )}
        
        <div className="text-xs text-gray-400">
          سيتم إنشاء:
          <br />• store2.localhost:3001 (إلكترونيات)
          <br />• store3.localhost:3001 (ملابس)
        </div>
      </div>
    </div>
  )
}
