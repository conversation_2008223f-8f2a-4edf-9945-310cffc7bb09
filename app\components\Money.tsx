'use client'

import { useState, useEffect } from 'react'
import { useData } from '@/contexts/DataContext'

interface MoneyProps {
  usdAmount: number
  className?: string
  showOriginal?: boolean
}

export function Money({ usdAmount, className = '', showOriginal = false }: MoneyProps) {
  const { convertPrice, formatPrice, selectedCurrency } = useData()
  const [convertedAmount, setConvertedAmount] = useState(usdAmount)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const convert = async () => {
      try {
        setLoading(true)
        const converted = await convertPrice(usdAmount)
        setConvertedAmount(converted)
      } catch (error) {
        console.error('Error converting price:', error)
        setConvertedAmount(usdAmount)
      } finally {
        setLoading(false)
      }
    }

    convert()
  }, [usdAmount, selectedCurrency, convertPrice])

  if (loading) {
    return <span className={`${className} animate-pulse`}>...</span>
  }

  return (
    <span className={className}>
      {formatPrice(convertedAmount)}
      {showOriginal && selectedCurrency !== 'USD' && (
        <span className="text-sm text-gray-500 ml-1">
          (${usdAmount.toFixed(2)})
        </span>
      )}
    </span>
  )
}
